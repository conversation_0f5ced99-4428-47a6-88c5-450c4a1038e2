# EmailJS Setup Guide - Complete Your Configuration

✅ **You already have:** Template ID = `template_ll729hh`
❌ **Still needed:** Service ID and Public Key

## Quick Setup Steps

### Step 1: Get Your Service ID

1. Go to your EmailJS dashboard: [https://dashboard.emailjs.com/](https://dashboard.emailjs.com/)
2. Click on **"Email Services"** in the left sidebar
3. If you don't have a service yet:
   - Click **"Add New Service"**
   - Choose **Gmail** (recommended)
   - Connect your Gmail account (<EMAIL>)
   - Give it a name like "Portfolio Contact"
4. Copy the **Service ID** (looks like `service_xxxxxxx`)

### Step 2: Get Your Public Key

1. In your EmailJS dashboard, click on **"Account"** in the left sidebar
2. Go to the **"General"** tab
3. Find the **"Public Key"** section
4. Copy your **Public Key** (looks like a long string of letters/numbers)

### Step 3: Verify Your Template

Since you already have template `template_ll729hh`, make sure it contains these variables:

**Required Template Content:**
```
Subject: New Contact Form Message from {{from_name}}

Hello Sreeraj,

You have received a new message from your portfolio contact form:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}

Message:
{{message}}

---
This message was sent from your portfolio contact form.
Reply directly to this email to respond to {{from_name}}.
```

**Template Settings:**
- To Email: `<EMAIL>`
- From Name: `{{from_name}}`
- Reply To: `{{from_email}}`

### Step 4: Update Your .env File

1. Open the `.env` file in your project root
2. Replace the placeholder values with your actual credentials:

```env
VITE_APP_EMAILJS_SERVICE_ID=service_your_actual_id
VITE_APP_EMAILJS_TEMPLATE_ID=template_ll729hh
VITE_APP_EMAILJS_PUBLIC_KEY=your_actual_public_key
```

### Step 5: Test the Form

1. Save the `.env` file
2. Restart your development server: `npm run dev`
3. Go to the contact section of your website
4. Fill out and submit the form
5. Check your email (<EMAIL>) for the message

## 🚀 Quick Links

- **EmailJS Dashboard**: [https://dashboard.emailjs.com/](https://dashboard.emailjs.com/)
- **Email Services**: [https://dashboard.emailjs.com/admin](https://dashboard.emailjs.com/admin)
- **Account Settings**: [https://dashboard.emailjs.com/account](https://dashboard.emailjs.com/account)

## ❓ Need Help Finding Your Credentials?

### Finding Service ID:
1. Dashboard → Email Services → Look for your service name
2. The Service ID is displayed next to your service name

### Finding Public Key:
1. Dashboard → Account → General tab
2. Look for "Public Key" section
3. Copy the entire key (usually starts with letters/numbers)

## 🔧 Troubleshooting

- **"Demo Mode" still showing**: Make sure all three credentials are in `.env` and restart server
- **Form not sending**: Check browser console for errors
- **Not receiving emails**: Verify your Gmail service is connected in EmailJS
- **Template errors**: Ensure your template has all the required variables listed above

## 🔒 Security Note

✅ Your `.env` file is already in `.gitignore` - your credentials won't be committed to Git
