# EmailJS Setup Guide - Almost Complete! 🎉

✅ **Template ID:** `template_ll729hh`
✅ **Public Key:** `DAkUN60QRtA6k1Zlm`
❌ **Still needed:** Service ID only!

## 🚀 Final Step - Get Your Service ID

### Option 1: Quick Test (Recommended)
1. **Open `test-emailjs.html`** in your browser
2. **Get Service ID** from [EmailJS Dashboard → Email Services](https://dashboard.emailjs.com/admin)
3. **Enter Service ID** in the test form and click "Test"
4. **If successful** → Copy the Service ID to your `.env` file

### Option 2: Manual Setup
1. **Go to EmailJS Dashboard:** [https://dashboard.emailjs.com/admin](https://dashboard.emailjs.com/admin)
2. **Find Email Services section**
3. **If you see a service:** Copy the Service ID (like `service_abc123`)
4. **If no service exists:**
   - Click "Add New Service"
   - Choose "Gmail"
   - Connect `<EMAIL>`
   - Copy the new Service ID

### Step 3: Verify Your Template

Since you already have template `template_ll729hh`, make sure it contains these variables:

**🚨 UPDATED Template Content (No Name Field):**
```
Subject: New Contact Form Message from {{from_name}}

Hello {{to_name}},

You have received a new message from your portfolio contact form:

Email: {{from_email}}
Phone: {{phone}}

Message:
{{message}}

---
This message was sent from your portfolio contact form.
Reply directly to this email to respond.
```

**Template Settings:**
- To Email: `<EMAIL>`
- From Name: `{{from_name}}`
- Reply To: `{{from_email}}`

### Step 4: Update Your .env File

1. Open the `.env` file in your project root
2. Replace the placeholder values with your actual credentials:

```env
VITE_APP_EMAILJS_SERVICE_ID=service_your_actual_id
VITE_APP_EMAILJS_TEMPLATE_ID=template_ll729hh
VITE_APP_EMAILJS_PUBLIC_KEY=your_actual_public_key
```

### Step 5: Test the Form

1. Save the `.env` file
2. Restart your development server: `npm run dev`
3. Go to the contact section of your website
4. Fill out and submit the form
5. Check your email (<EMAIL>) for the message

## 🚀 Quick Links

- **EmailJS Dashboard**: [https://dashboard.emailjs.com/](https://dashboard.emailjs.com/)
- **Email Services**: [https://dashboard.emailjs.com/admin](https://dashboard.emailjs.com/admin)
- **Account Settings**: [https://dashboard.emailjs.com/account](https://dashboard.emailjs.com/account)

## ❓ Need Help Finding Your Credentials?

### Finding Service ID:
1. Dashboard → Email Services → Look for your service name
2. The Service ID is displayed next to your service name

### Finding Public Key:
1. Dashboard → Account → General tab
2. Look for "Public Key" section
3. Copy the entire key (usually starts with letters/numbers)

## 🔧 Troubleshooting

- **"Demo Mode" still showing**: Make sure all three credentials are in `.env` and restart server
- **Form not sending**: Check browser console for errors
- **Not receiving emails**: Verify your Gmail service is connected in EmailJS
- **Template errors**: Ensure your template has all the required variables listed above

## 🔒 Security Note

✅ Your `.env` file is already in `.gitignore` - your credentials won't be committed to Git
