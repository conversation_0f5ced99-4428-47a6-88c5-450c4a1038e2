# EmailJS Setup Guide

This guide will help you set up EmailJS to make the contact form functional.

## Step 1: Create EmailJS Account

1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Click "Sign Up" and create a free account
3. Verify your email address

## Step 2: Add Email Service

1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail recommended)
4. Follow the setup instructions to connect your email account
5. Note down the **Service ID** (e.g., `service_abc123`)

## Step 3: Create Email Template

1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Use this template content:

```
Subject: New Contact Form Message from {{from_name}}

Hello Sreeraj,

You have received a new message from your portfolio contact form:

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}

Message:
{{message}}

---
This message was sent from your portfolio contact form.
Reply directly to this email to respond to {{from_name}}.
```

4. Set the "To Email" to: `<EMAIL>`
5. Set the "From Name" to: `{{from_name}}`
6. Set the "Reply To" to: `{{from_email}}`
7. Save the template and note down the **Template ID** (e.g., `template_xyz789`)

## Step 4: Get Public Key

1. Go to "Account" → "General"
2. Find your **Public Key** (e.g., `abcdefghijk123456`)

## Step 5: Update Environment Variables

1. Open the `.env` file in your project root
2. Replace the placeholder values with your actual EmailJS credentials:

```env
VITE_APP_EMAILJS_SERVICE_ID=your_actual_service_id
VITE_APP_EMAILJS_TEMPLATE_ID=your_actual_template_id
VITE_APP_EMAILJS_PUBLIC_KEY=your_actual_public_key
```

## Step 6: Test the Form

1. Restart your development server: `npm run dev`
2. Fill out the contact form on your website
3. Submit the form
4. Check your email (<EMAIL>) for the message

## Troubleshooting

- **Form not sending**: Check browser console for errors
- **Not receiving emails**: Verify your EmailJS service is properly connected
- **Template errors**: Ensure all template variables match the ones used in the code
- **Rate limiting**: EmailJS free plan has monthly limits

## Security Note

The `.env` file contains sensitive information. Make sure to:
1. Add `.env` to your `.gitignore` file
2. Never commit your actual EmailJS credentials to version control
3. Use environment variables in production deployment
