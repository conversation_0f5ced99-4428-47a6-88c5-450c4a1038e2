import { motion } from "framer-motion";

import { styles } from "../styles";
import { SectionWrapper } from "../hoc";
import { fadeIn, textVariant } from "../utils/motion";
import { techQuotes } from "../constants";

const QuoteCard = ({
  index,
  quote,
  author,
  title,
  company,
}) => (
  <motion.div
    variants={fadeIn("", "spring", index * 0.5, 0.75)}
    className='bg-black-200 p-10 rounded-3xl xs:w-[380px] w-full flex-shrink-0'
  >
    <p className='text-white font-black text-[48px]'>"</p>

    <div className='mt-1'>
      <p className='text-white tracking-wider text-[18px] leading-[28px] min-h-[140px] flex items-center'>
        {quote}
      </p>

      <div className='mt-7 flex justify-between items-center gap-1'>
        <div className='flex-1 flex flex-col'>
          <p className='text-white font-medium text-[16px]'>
            <span className='blue-text-gradient'>—</span> {author}
          </p>
          <p className='mt-1 text-secondary text-[12px]'>
            {title}{company && `, ${company}`}
          </p>
        </div>

        <div className='w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center'>
          <span className='text-white font-bold text-[14px]'>
            {author.split(' ').map(name => name[0]).join('').slice(0, 2)}
          </span>
        </div>
      </div>
    </div>
  </motion.div>
);

const TechQuotes = () => {
  return (
    <div className={`mt-12 bg-black-100 rounded-[20px]`}>
      <div
        className={`bg-tertiary rounded-2xl ${styles.padding} min-h-[300px]`}
      >
        <motion.div variants={textVariant()}>
          <p className={styles.sectionSubText}>Words of wisdom</p>
          <h2 className={styles.sectionHeadText}>Tech Inspiration.</h2>
        </motion.div>
      </div>
      <div className={`-mt-20 pb-14 ${styles.paddingX}`}>
        <div className='overflow-x-auto pb-6'>
          <div className='flex gap-7 min-w-max'>
            {techQuotes.map((quote, index) => (
              <QuoteCard key={`${quote.author}-${index}`} index={index} {...quote} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SectionWrapper(TechQuotes, "");
