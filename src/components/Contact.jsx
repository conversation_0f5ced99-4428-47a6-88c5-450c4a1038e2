import { useRef, useState } from "react";
import { motion } from "framer-motion";
import emailjs from "@emailjs/browser";

import { styles } from "../styles";
import { EarthCanvas } from "./canvas";
import { SectionWrapper } from "../hoc";
import { slideIn } from "../utils/motion";

const Contact = () => {
  const formRef = useRef();
  const [form, setForm] = useState({
    email: "",
    phone: "",
    message: "",
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [submitStatus, setSubmitStatus] = useState(null); // 'success', 'error', or null

  // Validation functions
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone) => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!form.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(form.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!form.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!validatePhone(form.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }

    if (!form.message.trim()) {
      newErrors.message = "Message is required";
    } else if (form.message.trim().length < 10) {
      newErrors.message = "Message must be at least 10 characters";
    } else if (form.message.trim().length > 1000) {
      newErrors.message = "Message must be less than 1000 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { target } = e;
    const { name, value } = target;

    setForm({
      ...form,
      [name]: value,
    });

    // Clear specific field error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }

    // Clear submit status when user makes changes
    if (submitStatus) {
      setSubmitStatus(null);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setSubmitStatus(null);

    // EmailJS configuration
    const serviceId = import.meta.env.VITE_APP_EMAILJS_SERVICE_ID;
    const templateId = import.meta.env.VITE_APP_EMAILJS_TEMPLATE_ID;
    const publicKey = import.meta.env.VITE_APP_EMAILJS_PUBLIC_KEY;

    // Check if EmailJS is configured
    if (!serviceId || !templateId || !publicKey ||
        serviceId === "your_service_id_here" ||
        templateId === "your_template_id_here" ||
        publicKey === "your_public_key_here") {

      // Demo mode - simulate email sending
      console.log("Demo Mode - Form Data:", {
        email: form.email,
        phone: form.phone,
        message: form.message
      });

      setTimeout(() => {
        setLoading(false);
        setSubmitStatus('success');

        // Reset form
        setForm({
          email: "",
          phone: "",
          message: "",
        });
        setErrors({});

        // Auto-hide success message after 5 seconds
        setTimeout(() => {
          setSubmitStatus(null);
        }, 5000);
      }, 1500); // Simulate network delay

      return;
    }

    // Real EmailJS sending
    emailjs
      .send(
        serviceId,
        templateId,
        {
          from_name: form.email.split('@')[0], // Use email username as name
          to_name: "Sreeraj",
          from_email: form.email,
          to_email: "<EMAIL>",
          phone: form.phone,
          message: form.message,
          reply_to: form.email,
        },
        publicKey
      )
      .then(
        () => {
          setLoading(false);
          setSubmitStatus('success');

          // Reset form
          setForm({
            email: "",
            phone: "",
            message: "",
          });
          setErrors({});

          // Auto-hide success message after 5 seconds
          setTimeout(() => {
            setSubmitStatus(null);
          }, 5000);
        },
        (error) => {
          setLoading(false);
          setSubmitStatus('error');
          console.error("EmailJS Error:", error);

          // Auto-hide error message after 5 seconds
          setTimeout(() => {
            setSubmitStatus(null);
          }, 5000);
        }
      );
  };

  return (
    <div
      className={`xl:mt-12 flex xl:flex-row flex-col-reverse gap-10 overflow-hidden`}
    >
      <motion.div
        variants={slideIn("left", "tween", 0.2, 1)}
        className='flex-[0.75] bg-black-100 p-4 sm:p-6 lg:p-8 rounded-2xl'
      >
        <p className={styles.sectionSubText}>Get in touch</p>
        <h3 className={styles.sectionHeadText}>Contact.</h3>

        {/* Demo Notice */}
        {(!import.meta.env.VITE_APP_EMAILJS_SERVICE_ID ||
          import.meta.env.VITE_APP_EMAILJS_SERVICE_ID === "your_service_id_here") && (
          <div className='mt-4 p-3 bg-blue-600/20 border border-blue-500 rounded-lg'>
            <p className='text-blue-400 text-sm'>
              📧 Demo Mode: Form validation is active. To enable email sending, configure EmailJS (see EMAILJS_SETUP.md)
            </p>
          </div>
        )}

        {/* Success/Error Messages */}
        {submitStatus === 'success' && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className='mt-4 p-4 bg-green-600/20 border border-green-500 rounded-lg'
          >
            <p className='text-green-400 font-medium'>
              ✅ Thank you! Your message has been sent successfully. I'll get back to you as soon as possible.
            </p>
          </motion.div>
        )}

        {submitStatus === 'error' && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className='mt-4 p-4 bg-red-600/20 border border-red-500 rounded-lg'
          >
            <p className='text-red-400 font-medium'>
              ❌ Oops! Something went wrong. Please try again or contact me <NAME_EMAIL>
            </p>
          </motion.div>
        )}

        <form
          ref={formRef}
          onSubmit={handleSubmit}
          className='mt-8 sm:mt-12 flex flex-col gap-6 sm:gap-8'
        >
          <label className='flex flex-col'>
            <span className='text-white font-medium mb-4'>Your Email *</span>
            <input
              type='email'
              name='email'
              value={form.email}
              onChange={handleChange}
              placeholder="What's your email address?"
              className={`bg-tertiary py-4 px-6 placeholder:text-secondary text-white rounded-lg outline-none border-none font-medium ${
                errors.email ? 'border-2 border-red-500' : ''
              }`}
            />
            {errors.email && (
              <span className='text-red-400 text-sm mt-2'>{errors.email}</span>
            )}
          </label>

          <label className='flex flex-col'>
            <span className='text-white font-medium mb-4'>Your Phone Number *</span>
            <input
              type='tel'
              name='phone'
              value={form.phone}
              onChange={handleChange}
              placeholder="What's your phone number?"
              className={`bg-tertiary py-4 px-6 placeholder:text-secondary text-white rounded-lg outline-none border-none font-medium ${
                errors.phone ? 'border-2 border-red-500' : ''
              }`}
            />
            {errors.phone && (
              <span className='text-red-400 text-sm mt-2'>{errors.phone}</span>
            )}
          </label>

          <label className='flex flex-col'>
            <span className='text-white font-medium mb-4'>Your Message *</span>
            <textarea
              rows={7}
              name='message'
              value={form.message}
              onChange={handleChange}
              placeholder='What would you like to say? (10-1000 characters)'
              className={`bg-tertiary py-4 px-6 placeholder:text-secondary text-white rounded-lg outline-none border-none font-medium resize-none ${
                errors.message ? 'border-2 border-red-500' : ''
              }`}
            />
            {errors.message && (
              <span className='text-red-400 text-sm mt-2'>{errors.message}</span>
            )}
            <span className='text-secondary text-xs mt-2'>
              {form.message.length}/1000 characters
            </span>
          </label>

          <button
            type='submit'
            disabled={loading}
            className={`py-3 px-8 rounded-xl outline-none w-fit text-white font-bold shadow-md shadow-primary transition-all duration-300 ${
              loading
                ? 'bg-gray-600 cursor-not-allowed'
                : 'bg-tertiary hover:bg-tertiary/80 hover:shadow-lg'
            }`}
          >
            {loading ? "Sending..." : "Send Message"}
          </button>
        </form>
      </motion.div>

      <motion.div
        variants={slideIn("right", "tween", 0.2, 1)}
        className='xl:flex-1 xl:h-auto md:h-[550px] h-[350px]'
      >
        <EarthCanvas />
      </motion.div>
    </div>
  );
};

export default SectionWrapper(Contact, "contact");
