<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>EmailJS Test for Portfolio Contact Form</h1>
        <p><strong>Template ID:</strong> template_ll729hh ✅</p>
        
        <form id="testForm">
            <label>Service ID:</label>
            <input type="text" id="serviceId" placeholder="service_xxxxxxx" required>
            
            <label>Public Key:</label>
            <input type="text" id="publicKey" placeholder="Your public key" required>
            
            <label>Test Name:</label>
            <input type="text" id="testName" value="Test User" required>
            
            <label>Test Email:</label>
            <input type="email" id="testEmail" value="<EMAIL>" required>
            
            <label>Test Phone:</label>
            <input type="tel" id="testPhone" value="+1234567890" required>
            
            <label>Test Message:</label>
            <textarea id="testMessage" rows="4" required>This is a test message from the EmailJS configuration test.</textarea>
            
            <button type="submit">Test EmailJS Configuration</button>
        </form>
        
        <div id="status"></div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 5px;">
            <h3>Instructions:</h3>
            <ol>
                <li>Get your <strong>Service ID</strong> from EmailJS Dashboard → Email Services</li>
                <li>Get your <strong>Public Key</strong> from EmailJS Dashboard → Account → General</li>
                <li>Fill in the fields above and click "Test"</li>
                <li>If successful, copy these values to your <code>.env</code> file</li>
                <li>Delete this test file after setup</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const serviceId = document.getElementById('serviceId').value;
            const publicKey = document.getElementById('publicKey').value;
            const templateId = 'template_ll729hh';
            
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<div class="status">Testing EmailJS configuration...</div>';
            
            // Initialize EmailJS
            emailjs.init(publicKey);
            
            // Test email data
            const templateParams = {
                from_name: document.getElementById('testName').value,
                to_name: 'Sreeraj',
                from_email: document.getElementById('testEmail').value,
                to_email: '<EMAIL>',
                phone: document.getElementById('testPhone').value,
                message: document.getElementById('testMessage').value,
                reply_to: document.getElementById('testEmail').value
            };
            
            emailjs.send(serviceId, templateId, templateParams)
                .then(function(response) {
                    statusDiv.innerHTML = `
                        <div class="status success">
                            <strong>✅ Success!</strong><br>
                            EmailJS is working correctly.<br>
                            Status: ${response.status}<br>
                            <br>
                            <strong>Next steps:</strong><br>
                            1. Check your email (<EMAIL>)<br>
                            2. Update your .env file with these credentials:<br>
                            <code>VITE_APP_EMAILJS_SERVICE_ID=${serviceId}</code><br>
                            <code>VITE_APP_EMAILJS_PUBLIC_KEY=${publicKey}</code><br>
                            3. Restart your development server<br>
                            4. Delete this test file
                        </div>
                    `;
                })
                .catch(function(error) {
                    statusDiv.innerHTML = `
                        <div class="status error">
                            <strong>❌ Error!</strong><br>
                            ${error.text || error.message || 'Unknown error'}<br>
                            <br>
                            <strong>Common issues:</strong><br>
                            • Wrong Service ID or Public Key<br>
                            • Email service not properly connected<br>
                            • Template variables don't match<br>
                            • Check EmailJS dashboard for service status
                        </div>
                    `;
                    console.error('EmailJS Error:', error);
                });
        });
    </script>
</body>
</html>
