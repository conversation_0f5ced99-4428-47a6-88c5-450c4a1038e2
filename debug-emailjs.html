<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Template Debug</title>
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 EmailJS Template Debug Tool</h1>
        
        <div class="info">
            <strong>Current Configuration:</strong><br>
            Service ID: service_epy2v66<br>
            Template ID: template_ll729hh<br>
            Public Key: DAkUN60QRtA6k1Zlm
        </div>

        <h3>Template Variables We're Sending:</h3>
        <pre id="templateVars">{
  "from_name": "test.user",
  "to_name": "Sreeraj", 
  "from_email": "<EMAIL>",
  "to_email": "<EMAIL>",
  "phone": "+1234567890",
  "message": "This is a test message",
  "reply_to": "<EMAIL>"
}</pre>

        <button onclick="testTemplate()">🧪 Test Current Template</button>
        <button onclick="testMinimal()">🔬 Test Minimal Template</button>
        
        <div id="results"></div>

        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
            <h3>🚨 Common Issues & Solutions:</h3>
            <ul>
                <li><strong>400 Error:</strong> Template variables don't match what's in your EmailJS template</li>
                <li><strong>Template Missing Variables:</strong> Your template might still expect a "name" field</li>
                <li><strong>Service Not Connected:</strong> Gmail service might not be properly connected</li>
            </ul>
            
            <h4>📝 Recommended Template Content:</h4>
            <pre>Subject: New Contact Form Message from {{from_name}}

Hello {{to_name}},

You have received a new message from your portfolio contact form:

Email: {{from_email}}
Phone: {{phone}}

Message:
{{message}}

---
This message was sent from your portfolio contact form.
Reply directly to this email to respond.</pre>
        </div>
    </div>

    <script>
        const serviceId = 'service_epy2v66';
        const templateId = 'template_ll729hh';
        const publicKey = 'DAkUN60QRtA6k1Zlm';
        
        emailjs.init(publicKey);

        function testTemplate() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Testing current template...</div>';
            
            const templateParams = {
                from_name: "test.user",
                to_name: "Sreeraj",
                from_email: "<EMAIL>",
                to_email: "<EMAIL>",
                phone: "+1234567890",
                message: "This is a test message from the debug tool",
                reply_to: "<EMAIL>"
            };

            emailjs.send(serviceId, templateId, templateParams)
                .then(function(response) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ SUCCESS!</strong><br>
                            Status: ${response.status}<br>
                            Text: ${response.text}<br>
                            <br>
                            Your template is working correctly! The issue might be with form validation or other code.
                        </div>
                    `;
                })
                .catch(function(error) {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ ERROR!</strong><br>
                            Status: ${error.status}<br>
                            Text: ${error.text}<br>
                            Message: ${error.message}<br>
                            <br>
                            <strong>Debug Info:</strong><br>
                            <pre>${JSON.stringify(error, null, 2)}</pre>
                            <br>
                            <strong>Next Steps:</strong><br>
                            1. Check your EmailJS template for missing variables<br>
                            2. Verify your Gmail service is connected<br>
                            3. Try the minimal test below
                        </div>
                    `;
                });
        }

        function testMinimal() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">Testing minimal template...</div>';
            
            // Minimal test with just basic variables
            const templateParams = {
                from_email: "<EMAIL>",
                message: "Minimal test message"
            };

            emailjs.send(serviceId, templateId, templateParams)
                .then(function(response) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ MINIMAL TEST SUCCESS!</strong><br>
                            Your basic template works. The issue is with specific variables.
                        </div>
                    `;
                })
                .catch(function(error) {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ MINIMAL TEST FAILED!</strong><br>
                            This indicates a fundamental issue with your template or service.<br>
                            <br>
                            Error: ${error.text || error.message}<br>
                            <br>
                            <strong>Recommendation:</strong> Recreate your EmailJS template with only basic variables.
                        </div>
                    `;
                });
        }
    </script>
</body>
</html>
